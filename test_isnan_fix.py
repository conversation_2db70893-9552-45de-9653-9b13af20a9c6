#!/usr/bin/env python3
"""
Test script to verify the isnan error fixes in the EEI formulation code.
"""

import numpy as np
import sys
import os

# Add current directory to path to import modules
sys.path.insert(0, os.getcwd())

from eeimpcalc import eeimpcalc

def test_nanaware_corrcoef():
    """Test the fixed nanaware_corrcoef function"""
    print("Testing nanaware_corrcoef function...")
    
    # Import the function from the main script
    exec(open('a6_load_multilas_EEI_XCOR_PLOT_Final.py').read(), globals())
    
    # Test cases
    test_cases = [
        # Normal case
        (np.array([1, 2, 3, 4, 5]), np.array([2, 4, 6, 8, 10]), "Normal arrays"),
        
        # Arrays with NaN
        (np.array([1, 2, np.nan, 4, 5]), np.array([2, 4, np.nan, 8, 10]), "Arrays with NaN"),
        
        # Arrays with None (should be handled)
        (np.array([1, 2, 3, 4, 5]), np.array([2, 4, 6, 8, None]), "Array with None"),
        
        # Empty arrays
        (np.array([]), np.array([]), "Empty arrays"),
        
        # Constant arrays
        (np.array([5, 5, 5, 5, 5]), np.array([2, 4, 6, 8, 10]), "Constant array"),
        
        # Mixed types (should be converted)
        ([1, 2, 3, 4, 5], [2, 4, 6, 8, 10], "List inputs"),
    ]
    
    for x, y, description in test_cases:
        try:
            result = nanaware_corrcoef(x, y)
            print(f"✅ {description}: {result}")
        except Exception as e:
            print(f"❌ {description}: Error - {e}")
    
    print()

def test_eeimpcalc_validation():
    """Test the eeimpcalc function with various inputs"""
    print("Testing eeimpcalc input validation...")
    
    # Create sample data
    pvel = np.array([3000, 3100, 3200, 3300, 3400])
    svel = np.array([1500, 1550, 1600, 1650, 1700])
    dens = np.array([2.2, 2.3, 2.4, 2.5, 2.6])
    
    test_cases = [
        # Normal case
        (pvel, svel, dens, 30, 0.25, 1, "Normal inputs"),
        
        # None k value
        (pvel, svel, dens, 30, None, 1, "None k value"),
        
        # Invalid k value
        (pvel, svel, dens, 30, -0.1, 1, "Negative k value"),
        
        # None angle
        (pvel, svel, dens, None, 0.25, 1, "None angle"),
        
        # Arrays with NaN
        (np.array([3000, np.nan, 3200]), np.array([1500, 1550, np.nan]), np.array([2.2, 2.3, 2.4]), 30, 0.25, 1, "Arrays with NaN"),
        
        # Empty arrays
        (np.array([]), np.array([]), np.array([]), 30, 0.25, 1, "Empty arrays"),
    ]
    
    for pv, sv, d, ang, k, method, description in test_cases:
        try:
            eei, egi, scaler = eeimpcalc(pv, sv, d, ang, k, method)
            if np.all(np.isnan(eei)):
                print(f"⚠️  {description}: Returned NaN (expected for invalid inputs)")
            else:
                print(f"✅ {description}: Success - EEI shape: {eei.shape}")
        except Exception as e:
            print(f"❌ {description}: Error - {e}")
    
    print()

def test_k_value_validation():
    """Test k value validation in optimization functions"""
    print("Testing k value validation...")
    
    # Test data
    depth = np.array([1000, 1001, 1002, 1003, 1004])
    dt = np.array([200, 210, 220, 230, 240])  # microseconds/ft
    dts = np.array([400, 420, 440, 460, 480])  # microseconds/ft
    rhob = np.array([2.2, 2.3, 2.4, 2.5, 2.6])
    target = np.array([50, 55, 60, 65, 70])
    
    # Import the function from the main script
    exec(open('a6_load_multilas_EEI_XCOR_PLOT_Final.py').read(), globals())
    
    test_cases = [
        # Normal case with k_method=1
        (1, None, "Calculate k from logs"),
        
        # Constant k value
        (2, 0.25, "Valid constant k"),
        
        # Invalid constant k value (None)
        (2, None, "None constant k"),
        
        # Invalid constant k value (negative)
        (2, -0.1, "Negative constant k"),
    ]
    
    for k_method, k_value, description in test_cases:
        try:
            result = calculate_eei_optimum_angle_merged(depth, dt, dts, rhob, target, 3, k_method, k_value)
            if result[0] is not None:
                print(f"✅ {description}: Optimum angle = {result[0]}°")
            else:
                print(f"⚠️  {description}: No optimum angle found (expected for some invalid inputs)")
        except Exception as e:
            print(f"❌ {description}: Error - {e}")
    
    print()

if __name__ == "__main__":
    print("🔧 Testing isnan error fixes...\n")
    
    test_nanaware_corrcoef()
    test_eeimpcalc_validation()
    test_k_value_validation()
    
    print("✅ All tests completed!")